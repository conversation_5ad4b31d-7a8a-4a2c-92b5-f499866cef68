#!/bin/bash
# Production File Permissions Script for RajaGenWeb
# Run this script on your production server after uploading files

echo "Setting file permissions for RajaGenWeb production deployment..."

# Set default permissions for files
echo "Setting file permissions (644)..."
find . -type f -name "*.php" -exec chmod 644 {} \;
find . -type f -name "*.html" -exec chmod 644 {} \;
find . -type f -name "*.css" -exec chmod 644 {} \;
find . -type f -name "*.js" -exec chmod 644 {} \;
find . -type f -name "*.json" -exec chmod 644 {} \;
find . -type f -name "*.xml" -exec chmod 644 {} \;
find . -type f -name "*.txt" -exec chmod 644 {} \;
find . -type f -name "*.md" -exec chmod 644 {} \;

# Set permissions for image files
echo "Setting image file permissions..."
find . -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.ico" \) -exec chmod 644 {} \;

# Set directory permissions
echo "Setting directory permissions (755)..."
find . -type d -exec chmod 755 {} \;

# Set writable permissions for upload directories
echo "Setting writable directory permissions (777)..."
chmod -R 777 uploads/
chmod -R 777 profile/
chmod -R 777 templates_c/
chmod -R 777 includes/backup/

# Create upload subdirectories if they don't exist
echo "Creating upload directories..."
mkdir -p uploads/application/logo/
mkdir -p uploads/application/file/
mkdir -p uploads/application/description/
mkdir -p uploads/images/
mkdir -p uploads/json/
mkdir -p uploads/notification/
mkdir -p uploads/note_value/

# Set permissions for upload subdirectories
chmod -R 777 uploads/application/
chmod -R 777 uploads/images/
chmod -R 777 uploads/json/
chmod -R 777 uploads/notification/
chmod -R 777 uploads/note_value/

# Set restrictive permissions for sensitive config files
echo "Setting restrictive permissions for config files..."
chmod 600 includes/db_config.php
chmod 644 includes/config.php
chmod 644 includes/functions.php
chmod 644 includes/mysql.class.php

# Set permissions for .htaccess
chmod 644 .htaccess

# Set permissions for index files
chmod 644 index.php

# Make sure error log is writable
touch error.log
chmod 666 error.log

# Set permissions for API directory
if [ -d "api" ]; then
    chmod 755 api/
    find api/ -type f -name "*.php" -exec chmod 644 {} \;
fi

# Set permissions for serverside directory
if [ -d "serverside" ]; then
    chmod 755 serverside/
    find serverside/ -type f -name "*.php" -exec chmod 644 {} \;
    find serverside/ -type d -exec chmod 755 {} \;
fi

# Set permissions for content directory
if [ -d "content" ]; then
    chmod 755 content/
    find content/ -type f -name "*.php" -exec chmod 644 {} \;
    find content/ -type d -exec chmod 755 {} \;
fi

echo "File permissions set successfully!"
echo ""
echo "Summary of permissions:"
echo "- PHP/HTML/CSS/JS files: 644"
echo "- Directories: 755"
echo "- Upload directories: 777"
echo "- Config files: 600-644"
echo "- Error log: 666"
echo ""
echo "Please verify that your web server can read all files and write to upload directories."
