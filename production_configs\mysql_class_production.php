<?php
/**
 * Production MySQL Class Base URL Function
 * Replace the base_url() function in includes/mysql.class.php (around line 345)
 */

function base_url()
{
    $protocol = (isset($_SERVER['HTTPS']) && ($_SERVER['HTTPS'] == 'on')) ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];

    // Production: Remove XAMPP subdirectory logic
    $script_path = dirname($_SERVER['SCRIPT_NAME']);

    // Handle Windows and Unix path separators
    if ($script_path == '/' || $script_path == '\\') {
        $script_path = '';
    }

    // Production: Remove XAMPP-specific subdirectory detection
    // Comment out or remove this block:
    /*
    if (defined('DOC_ROOT_PATH') && strpos(DOC_ROOT_PATH, '/RajaGenWeb/') !== false) {
        $script_path = '/RajaGenWeb';
    }
    */

    $Folder = $protocol . $host . $script_path . '/';

    return $Folder;
}
?>
