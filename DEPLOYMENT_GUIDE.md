# RajaGenWeb Production Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying your RajaGenWeb application from XAMPP development environment to a production server (VPS or shared hosting).

## Pre-Deployment Checklist

### 1. Database Configuration Changes

#### File: `includes/db_config.php`
**Current (XAMPP):**
```php
<?php
date_default_timezone_set('Asia/Riyadh');
$DB_ip = "************";
$DB_host = "localhost";
$DB_user = "root";
$DB_pass = "";
$DB_name = "globalne_itunnel";
```

**Production Changes:**
```php
<?php
date_default_timezone_set('Asia/Riyadh'); // Keep or change as needed
$DB_ip = "YOUR_PRODUCTION_SERVER_IP";
$DB_host = "localhost"; // Or your database host
$DB_user = "your_db_username";
$DB_pass = "your_secure_db_password";
$DB_name = "your_production_db_name";
```

### 2. File Path Modifications

#### File: `includes/functions.php`
**Current (Line 2):**
```php
define('DOC_ROOT_PATH', $_SERVER['DOCUMENT_ROOT'].'/RajaGenWeb/');
```

**Production Change:**
```php
define('DOC_ROOT_PATH', $_SERVER['DOCUMENT_ROOT'].'/');
```

#### File: `includes/mysql.class.php` (Lines 360-363)
**Current:**
```php
if (defined('DOC_ROOT_PATH') && strpos(DOC_ROOT_PATH, '/RajaGenWeb/') !== false) {
    $script_path = '/RajaGenWeb';
}
```

**Production Change:**
```php
// Remove or comment out this XAMPP-specific code
// if (defined('DOC_ROOT_PATH') && strpos(DOC_ROOT_PATH, '/RajaGenWeb/') !== false) {
//     $script_path = '/RajaGenWeb';
// }
```

### 3. .htaccess Production Configuration

#### File: `.htaccess`
**Current (Line 2):**
```apache
RewriteBase /RajaGenWeb/
```

**Production Change:**
```apache
RewriteBase /
```

**Complete Production .htaccess:**
```apache
RewriteEngine On
RewriteBase /

# Force HTTPS (recommended for production)
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Block direct access to sensitive directories
RewriteRule ^(includes|content|templates|templates_c|uploads)/ - [F,L]

# Main URL rewriting rule
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/.]+)/?$ index.php?p=$1 [L,QSA]

# Handle activation links
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^activate/([0-9]+)/([^/.]+)/?$ index.php?p=activate&code=$1&email=$2 [L,QSA]

# Handle support ticket links
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^supportticket/(.*)/([^/.]+)/?$ index.php?p=supportticket&id=$1&user=$2 [L,QSA]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options nosniff
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';"
</IfModule>

# Deny access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|phps|fla|psd|log|sh|sql|md)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Deny access to config files
<FilesMatch "^(config|db_config)\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>
```

### 4. Remove XAMPP-Specific Files

Delete these files before deployment:
- `httpd-xampp-backup.conf`
- `php_ini_backup.ini`
- `test_ajax_endpoints.php`
- `test_manual_load.php`
- `test_ssh2.php`
- `test_ssh_wrapper.php`
- `phpinfo_test.php`
- `responsive_test.html`
- `debug_extensions.php`

### 5. File Permissions (Linux/Unix hosting)

Set the following permissions:
```bash
# Application files
find . -type f -name "*.php" -exec chmod 644 {} \;
find . -type f -name "*.html" -exec chmod 644 {} \;
find . -type f -name "*.css" -exec chmod 644 {} \;
find . -type f -name "*.js" -exec chmod 644 {} \;

# Directories
find . -type d -exec chmod 755 {} \;

# Writable directories
chmod 777 uploads/
chmod 777 uploads/application/
chmod 777 uploads/images/
chmod 777 uploads/json/
chmod 777 uploads/notification/
chmod 777 profile/
chmod 777 templates_c/
chmod 777 includes/backup/

# Config files (more restrictive)
chmod 600 includes/db_config.php
chmod 644 includes/config.php
```

### 6. Environment-Specific Settings

#### Error Reporting (Production)
In production, disable error display. Add to top of `includes/config.php`:
```php
<?php
// Production error settings
error_reporting(E_ALL);
ini_set('display_errors', '0');
ini_set('log_errors', '1');
ini_set('error_log', 'error.log');
```

#### PHP Configuration
Ensure your hosting provider has these settings:
- `max_execution_time = 300`
- `max_input_time = 300`
- `post_max_size = 100M`
- `upload_max_filesize = 100M`
- `memory_limit = 256M`

### 7. Security Hardening

#### Additional Security Files
Create `robots.txt` in root:
```
User-agent: *
Disallow: /includes/
Disallow: /content/
Disallow: /templates/
Disallow: /templates_c/
Disallow: /api/
Disallow: /serverside/
```

#### Database Security
1. Create a dedicated database user with minimal privileges
2. Use strong passwords
3. Enable SSL connections if available
4. Regular database backups

### 8. SSL Certificate
- Install SSL certificate for HTTPS
- Update any hardcoded HTTP URLs to HTTPS
- Enable HTTPS redirect in .htaccess

## Deployment Steps

### Step 1: Prepare Files
1. Make all configuration changes listed above
2. Remove XAMPP-specific files
3. Clear Smarty cache: `rm -rf templates_c/*`
4. Test locally after changes

### Step 2: Database Setup
1. Create production database
2. Import your database dump
3. Update database credentials in `includes/db_config.php`
4. Test database connection

### Step 3: Upload Files
1. Upload all files to production server
2. Set correct file permissions
3. Ensure upload directories are writable

### Step 4: Domain Configuration
1. Point domain to your hosting
2. Configure DNS settings
3. Install SSL certificate

### Step 5: Final Testing
1. Test all major functionality
2. Check AJAX endpoints
3. Verify file uploads work
4. Test user registration/login
5. Check email functionality

## Post-Deployment Checklist

- [ ] Database connection working
- [ ] All pages loading correctly
- [ ] AJAX functionality working
- [ ] File uploads working
- [ ] Email notifications working
- [ ] SSL certificate installed
- [ ] Error logging configured
- [ ] Backup system in place
- [ ] Security headers active
- [ ] Performance optimized

## Troubleshooting

### Common Issues:
1. **AJAX 404 errors**: Check base URL configuration
2. **File permission errors**: Verify directory permissions
3. **Database connection failed**: Check credentials and host
4. **Images not loading**: Check upload directory permissions
5. **500 Internal Server Error**: Check error logs and .htaccess syntax

### Log Files to Monitor:
- Server error logs
- Application error logs (`error.log`)
- Database logs
- Access logs

## Maintenance

### Regular Tasks:
1. Database backups
2. File backups
3. Security updates
4. Log file cleanup
5. Performance monitoring

This completes the deployment guide. Follow each step carefully and test thoroughly before going live.
