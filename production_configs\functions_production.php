<?php
/**
 * Production Functions Configuration
 * Replace the first few lines of includes/functions.php with this content
 */

// Production error handling
error_reporting(E_ALL);
ini_set('display_errors', '0');  // Don't display errors to users
ini_set('log_errors', '1');      // Log errors to file
ini_set('error_log', 'error.log'); // Error log file

// Production document root path (remove /RajaGenWeb/ subdirectory)
define('DOC_ROOT_PATH', $_SERVER['DOCUMENT_ROOT'].'/');

// Include configuration
include DOC_ROOT_PATH . './includes/config.php';

// Include Mobile Detect library
require_once DOC_ROOT_PATH . './includes/Mobile_Detect.php';
$detect = new Mobile_Detect;

// Rest of the functions.php file remains the same...
?>
