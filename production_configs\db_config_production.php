<?php
/**
 * Production Database Configuration
 * Replace the content of includes/db_config.php with this file's content
 */

// Set timezone for your production server location
date_default_timezone_set('Asia/Riyadh'); // Change as needed

// Production Database Configuration
// IMPORTANT: Replace these values with your actual production database credentials
$DB_ip = "YOUR_PRODUCTION_SERVER_IP";        // Your production server IP
$DB_host = "localhost";                       // Usually localhost, or your database host
$DB_user = "your_production_db_user";         // Your production database username
$DB_pass = "your_secure_database_password";   // Your production database password
$DB_name = "your_production_database_name";   // Your production database name

// Create database connection
$mysqli = new MySQLi($DB_host, $DB_user, $DB_pass, $DB_name);

// Check connection
if($mysqli->connect_error){
   // In production, log errors instead of displaying them
   error_log('Database Connection Error: ('. $mysqli->connect_errno .') '. $mysqli->connect_error);
   die('Database connection failed. Please contact administrator.');
}

// Set charset to UTF8 for proper character handling
$mysqli->set_charset("utf8");
?>
