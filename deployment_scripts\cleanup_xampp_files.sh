#!/bin/bash
# XAMPP Cleanup Script for Production Deployment
# This script removes XAMPP-specific files that should not be deployed to production

echo "Cleaning up XAMPP-specific files for production deployment..."

# Remove XAMPP configuration files
echo "Removing XAMPP configuration files..."
rm -f httpd-xampp-backup.conf
rm -f php_ini_backup.ini

# Remove test files
echo "Removing test files..."
rm -f test_ajax_endpoints.php
rm -f test_manual_load.php
rm -f test_ssh2.php
rm -f test_ssh_wrapper.php
rm -f phpinfo_test.php
rm -f responsive_test.html
rm -f debug_extensions.php
rm -f debug_routing.php

# Remove development/debug files
echo "Removing development files..."
rm -f cookies.txt
rm -f error_log

# Remove SSH2 extension files (if not needed in production)
echo "Removing SSH2 extension files..."
rm -f php_ssh2.dll
rm -f php_ssh2.pdb
rm -f php_ssh2.zip
rm -f php_ssh2_81.zip

# Remove SQL dump files (move to backup location instead)
echo "Moving SQL files to backup..."
mkdir -p backup_sql/
mv *.sql backup_sql/ 2>/dev/null || true

# Remove backup configuration files
echo "Removing backup configuration files..."
rm -f php.ini.bak
rm -f .htaccess.bak

# Remove temporary files
echo "Removing temporary files..."
find . -name "*.tmp" -delete
find . -name "*.bak" -delete
find . -name "*.backup" -delete
find . -name "*.old" -delete
find . -name "*.orig" -delete
find . -name "*.save" -delete
find . -name "*.swp" -delete

# Remove Thumbs.db and .DS_Store files
echo "Removing system files..."
find . -name "Thumbs.db" -delete
find . -name ".DS_Store" -delete

# Remove empty directories
echo "Removing empty directories..."
find . -type d -empty -delete 2>/dev/null || true

# Clear Smarty template cache
echo "Clearing Smarty template cache..."
rm -rf templates_c/*.php

# Remove version control files if present
echo "Removing version control files..."
rm -rf .git/
rm -f .gitignore
rm -f .gitattributes

# Remove composer development files if present
echo "Removing composer development files..."
rm -f composer.json
rm -f composer.lock
rm -rf vendor/

# Remove node.js development files if present
echo "Removing Node.js development files..."
rm -f package.json
rm -f package-lock.json
rm -rf node_modules/

# Remove IDE files
echo "Removing IDE files..."
rm -rf .vscode/
rm -rf .idea/
rm -f *.sublime-project
rm -f *.sublime-workspace

# Remove log files (they will be recreated)
echo "Removing old log files..."
find . -name "*.log" -delete

# Create necessary directories if they don't exist
echo "Creating necessary directories..."
mkdir -p uploads/application/logo/
mkdir -p uploads/application/file/
mkdir -p uploads/application/description/
mkdir -p uploads/images/
mkdir -p uploads/json/
mkdir -p uploads/notification/
mkdir -p uploads/note_value/
mkdir -p profile/
mkdir -p templates_c/
mkdir -p includes/backup/

# Create .htaccess files for sensitive directories
echo "Creating security .htaccess files..."

# Protect includes directory
cat > includes/.htaccess << 'EOF'
Order allow,deny
Deny from all
EOF

# Protect templates_c directory
cat > templates_c/.htaccess << 'EOF'
Order allow,deny
Deny from all
EOF

# Protect backup directory
cat > includes/backup/.htaccess << 'EOF'
Order allow,deny
Deny from all
EOF

echo ""
echo "XAMPP cleanup completed successfully!"
echo ""
echo "Files removed:"
echo "- XAMPP configuration files"
echo "- Test and debug files"
echo "- Temporary files"
echo "- Development files"
echo "- Version control files"
echo "- IDE files"
echo ""
echo "Directories created:"
echo "- Upload directories with proper structure"
echo "- Security .htaccess files added"
echo ""
echo "Your application is now ready for production deployment!"
echo "Don't forget to:"
echo "1. Update database configuration"
echo "2. Update file paths"
echo "3. Set proper file permissions"
echo "4. Test thoroughly before going live"
