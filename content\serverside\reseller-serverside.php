<?php

chkSession();
if($user_id_2 == 1 || $user_level_2 == 'superadmin' || $user_level_2 == 'reseller'){
	
}else{
	header("Location: " . $db->base_url() . "dashboard");
}

$requestData= $_REQUEST;
if(empty($requestData)){
	$db->RedirectToURL($db->base_url());
	exit;	
}

$columns = array( 
    0	=> 'user_id',
	1	=> 'user_name',
	2	=> null
);

$sql = "SELECT * FROM users";
$query = $db->sql_query($sql) or die();
$totalData = $db->sql_numrows($query);
$totalFiltered = $totalData;
if($user_id_2 == 1 || $user_level_2 == 'superadmin'){
    $sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND user_level='reseller'";
}else{
	$sql = "SELECT * FROM users WHERE 1=1 AND user_id!='$user_id_2' AND upline='$user_id_2' AND user_level='reseller'";
}

if( !empty($requestData['search']['value']) ) { 
	$sql.=" AND ( user_id LIKE '%".$requestData['search']['value']."%' "; 
	$sql.=" OR user_name LIKE '%".$requestData['search']['value']."%' ) ";
}

$query = $db->sql_query($sql) or die();
$totalFiltered = $db->sql_numrows($query);

// Fix the ORDER BY clause to handle null columns
$orderColumn = $columns[$requestData['order'][0]['column']];
if($orderColumn == null) {
    $orderColumn = 'user_id'; // Default to user_id if column is null
}
$sql.=" ORDER BY ". $orderColumn."  ".$requestData['order'][0]['dir']."  LIMIT ".$requestData['start']." ,".$requestData['length']." ";

$query = $db->sql_query($sql) or die();


$data = array();
while( $row = $db->sql_fetchrow($query) ) {
	$nestedData=array();
	$userid = $row['user_id'];
	$username = $row['user_name'];
	$password = $row['user_pass'];
	$user_pass = $db->decrypt_key($password);
	$userpass = $db->encryptor('decrypt', $user_pass);
	$usercredits = $row['credits'];
	$is_freeze = $row['is_freeze'];
	
	$client_sql = "SELECT user_name FROM users WHERE upline='$userid' AND user_id!='$userid'";
	$client_qry = $db->sql_query("$client_sql") OR die();
	$client = $db->sql_numrows($client_qry);
	
	if($client == 0){
	    $total_client = 'No Client';
	}else{
	    $total_client = $client;
	}
	
	$sql2 = "SELECT profile_image FROM users_profile WHERE profile_id='$userid'";
    $qry2 = $db->sql_query("$sql2") OR die();
	$row2 = $db->sql_fetchrow($qry2);
	
	$reseller_image = $row2['profile_image'];
	
	if($reseller_image == ''){
	    $reseller_img = 'profile/avatar-1.png';
	}else{
        $reseller_img = 'profile/'.$userid.'/'.$reseller_image;
	}
	
	if($is_freeze == 0){
	    $is_blocked = '<span class="badge badge-success"><span class="fas fa-user"></span> Active</span>';
	    $icon_blocked = '';
	    $badge_blocked = 'primary';
	}else{
	    $is_blocked = '<span class="badge badge-danger"><span class="fas fa-user-slash"></span> Blocked</span>';
	    $icon_blocked = '<img src="dist/img/block.png" class="avatar-icon" alt="...">';
	    $badge_blocked = 'danger';
	}
	
	$nestedData[] = '<span style="white-space:nowrap">
	                <figure class="avatar mr-2 avatar-sm border-primary">
                      <img src="'.$reseller_img.'" alt="'.$username.'">
                      '.$icon_blocked.'
                    </figure> <span class="badge badge-'.$badge_blocked.'"><a class="username-class" onclick="view_info('.$userid.')">'.$username.'</a></span></span>';
	$nestedData[] = '<span class="badge badge-primary"><i class="fas fa-coins"></i> '.$usercredits.'</span>';
	$nestedData[] = '<span class="badge badge-primary"><i class="fas fa-users"></i> '.$total_client.'</span>';
	$nestedData[] = $is_blocked;
	$nestedData[] = '<div class="btn-group btn-group-md" role="group">
                    	<button type="button" class="btn btn-primary mr-1" onclick="view_info('.$userid.')"><i class="far fa-eye"></i></button>
                    	<button type="button" class="btn btn-success btn-copy" data-clipboard-text="Reseller Details 

Username : '.$username.' 
Password : '.$userpass.'
Credits : '.$usercredits.'
URL Link : '.$base_url.'/login" data-id="'.$userid.'"><i class="far fa-copy"></i></button>
                    </div>';

	$data[] = $nestedData;	
}

$json_data = array(
			"draw"            => intval( $requestData['draw'] )? intval( $_REQUEST['draw'] ) : 0,
			"recordsTotal"    => intval( $totalData ),
			"recordsFiltered" => intval( $totalFiltered ),
			"data"            => ($data )
			);

echo json_encode($json_data);
?>